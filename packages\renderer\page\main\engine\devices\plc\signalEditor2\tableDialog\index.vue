<template>
  <MyDialog v-model="isActive" title="PLC Calculation Editor" width="500px" @ok="onSave">
    <div :class="e('body')">
      <!-- <div>Signal: {{ signal }}</div>
      <div>Source: {{ source }}</div> -->

      <div style="display: flex; align-items: center; margin-bottom: 16px">
        <span style="margin-right: 8px">X Range</span>
        <wui-input v-model="model.min" placeholder="Min" style="width: 140px; margin-right: 8px" />
        <wui-input v-model="model.max" placeholder="Max" style="width: 140px" />
      </div>
      <div class="cfg-setup_table">
        <wui-table
          show-overflow-tooltip
          :data="model.data"
          height="250px"
          border
          @row-contextmenu="handleRowMenu"
        >
          <wui-table-column prop="x" label="X" min-width="120px" align="center">
            <template #default="{ row }">
              <wui-input v-model="row.x" placeholder="X" />
            </template>
          </wui-table-column>
          <wui-table-column prop="y" label="Y" min-width="120px" align="center">
            <template #default="{ row }">
              <wui-input v-model="row.y" placeholder="Y" />
            </template>
          </wui-table-column>
          <wui-table-column label="Op" width="100px" align="center">
            <template #default="{ row, $index }">
              <TableTool.Op :flag="row.flag" @op="handleOp($event, row, $index)" />
            </template>
          </wui-table-column>
          <template #empty>
            <TableTool.Empty @custom-contextmenu="handleTableAreaCtxMenu" />
          </template>
        </wui-table>
      </div>
    </div>
  </MyDialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, toRef, onMounted } from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import TableTool, { isAddOrInsertType, OpType } from '@/renderer/components/TableTool'
import { useBizEngine, usePermanentFocus, useTableCommonMenu } from '@/renderer/hooks'
import { WuiMessage } from '@wuk/wui'
import $styles from '../index.module.scss'
import { useBem } from '@/renderer/hooks/bem'

const { e } = useBem('plc-signal-table', $styles)

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  calibData: { type: Object, default: () => ({}) }
})

const emit = defineEmits(['update:modelValue', 'save'])

const isActive = ref(props.modelValue)
watch(
  () => props.modelValue,
  v => (isActive.value = v)
)
watch(isActive, v => emit('update:modelValue', v))

const bizEngine = useBizEngine()

const xMin = ref(props.calibData.min)
const xMax = ref(props.calibData.max)
const model = reactive({
  data: [] as any[],
  min: Number,
  max: Number
})
const plcList = ref<any[]>([])

const createRow = (row_type: any) => ({
  x: 0,
  y: 0,
  flag: true,
  row_type
})

const { handleInputClick, handleSelect } = usePermanentFocus<any>()
const { handleRowMenu, handleTableAreaCtxMenu } = useTableCommonMenu(
  toRef(model, 'data'),
  (key, ...args) => {
    const { row, rowIndex } = args[0]
    switch (key) {
      case 'addKey':
        model.data.push(createRow('add'))
        break
      case 'insertKey':
        model.data.splice(rowIndex + 1, 0, createRow('insert'))
        break
      case 'deleteKey':
        handleOp('delete', row, rowIndex)
        break
      case 'modifyKey':
        handleOp('edit', row, rowIndex)
        break
      default:
        break
    }
  },
  [1],
  [
    { key: 'addKey', label: 'add' },
    { key: 'insertKey', label: 'insert' },
    { key: 'modifyKey', label: 'modify' },
    { key: 'deleteKey', label: 'delete' }
  ]
)
const handleOp = (op: OpType, row: any, index: number) => {
  switch (op) {
    case 'edit':
      row.flag = true
      break
    case 'cancel':
      handleCancel(row, index)
      break
    case 'delete':
      handleDelete(index)
      break
    case 'select':
      handleSubmit(row, index)
      break
    default:
      break
  }
}
const handleCancel = (row: any, index: number) => {
  if (isAddOrInsertType(row.row_type)) {
    model.data.splice(index, 1)
    return
  }
  const plcItem = plcList.value[index]
  model.data[index] = {
    ...row,
    ...plcItem,
    flag: false
  }
}
const tipsMessage = () => {
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 90,
    grouping: true
  })
}
const handleDelete = async (index: number) => {
  const res = await bizEngine.value?.removeTimer(index)
  model.data.splice(index, 1)
  if (!res) return
  tipsMessage()
}
const handleSubmit = async (row: any, index: number) => {
  let res: boolean | undefined
  const { flag, row_type, ...params } = row
  if (isAddOrInsertType(row_type)) {
    // res = await bizEngine.value?.addTimer(params, index)
  } else {
    // res = await bizEngine.value?.modifyTimer(index, params)
  }
  if (!res) return
  row.flag = false
  tipsMessage()
}
const getDataInfo = async () => {
  const rawData = props.calibData.data
  const groupedList = []

  for (let i = 0; i < rawData.length; i += 2) {
    groupedList.push({
      x: rawData[i],
      y: rawData[i + 1],
      flag: false,
      row_type: '*'
    })
  }

  model.data = groupedList
  model.min = props.calibData.min
  model.max = props.calibData.max
  plcList.value = groupedList
}

const onSave = () => {
  emit('save', {
    xMin: xMin.value,
    xMax: xMax.value,
    table: model.data.map(row => ({ ...row }))
  })
  isActive.value = false
}

onMounted(() => {
  getDataInfo()
})
</script>
