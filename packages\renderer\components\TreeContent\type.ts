import { AppMenuItem } from '@wuk/cfg'
import { RenderContentContext } from '@wuk/wui'
export type TreeNode = RenderContentContext['node']
export interface Tree<T = string | number> {
  label: string
  id: T
  index?: number
  children?: Tree[]
}
export interface TreeProps {
  treeData?: Tree[]
  treeAreaMenu?: AppMenuItem[]
  showRightBorder?: boolean
  showRightMenu?: boolean
  rightContentPadding?: number
  draggable?: boolean
  allowDrag?: (node: TreeNode) => boolean
  allowDrop?: (
    draggingNode: TreeNode,
    dropNode: TreeNode,
    type: 'prev' | 'inner' | 'next'
  ) => boolean
}
export type CtxType = 'Node' | 'Area'
