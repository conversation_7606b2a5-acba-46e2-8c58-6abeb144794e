import { InjectionKey } from 'vue'
import { Tree } from '../../TreeContent'
import { CfgFileType } from '@wuk/cfg'
import { useBiz } from './hooks'
export interface CalcTreeChild extends Omit<Tree<number>, 'children'> {
  originData: {
    group_name: string
    type: CfgFileType
    file: string
  }
}
export interface CalcTree extends Tree<number> {
  children?: CalcTreeChild[]
}
export type ReadFnName = 'readCalcsInitOptions' | 'readCalcsFinalOptions' | 'readCalcsSignalOptions'
export type RemoveFnName = 'removeCalcsInit' | 'removeCalcsFinal' | 'removeCalcsSignal'
export type AddFnName = 'addCalcsInit' | 'addCalcsFinal' | 'addCalcsSignal'
export type ModifyFnName = 'modifyCalcsInit' | 'modifyCalcsFinal' | 'modifyCalcsSignal'
export type HandleEventName =
  | 'onCalcsInitialOChanged'
  | 'onCalcsFinalChanged'
  | 'onCalcsSignalChanged'
export type LoadCalcName = 'loadCalc'
export type ModifyCalcName = 'modifyCalc'
export type SaveCalcName = 'saveCalc'
type CalcModeItem = {
  readFnName: ReadFnName
  removeFnName: RemoveFnName
  addFnName: AddFnName
  modifyFnName: ModifyFnName
  handleEventName: HandleEventName
  loadCalcName: LoadCalcName
  modifyCalcName: ModifyCalcName
  saveCalcName: SaveCalcName
}
export type CurEditCalcInfo = {
  label: string
  calcId: number
  index?: number
  groupNodeIndex?: number
  children?: CalcTreeChild[]
}
interface calcContext {
  bizCalcs: ReturnType<typeof useBiz>
  calcMode: CalcModeType
  curEditCalcInfo: CurEditCalcInfo
  changeTreeNode: (id: number) => void
}

export const CalcMode = <const>{
  Initial: 'initial',
  Final: 'final',
  Signal: 'signal',
  Sys_common: 'sys_common'
}
export type CalcModeType = (typeof CalcMode)[keyof typeof CalcMode]
const commonFnName = {
  loadCalcName: 'loadCalc',
  modifyCalcName: 'modifyCalc',
  saveCalcName: 'saveCalc'
} as const
export const calcModefnMap: Record<string, CalcModeItem> = <const>{
  [CalcMode.Initial]: {
    readFnName: 'readCalcsInitOptions',
    removeFnName: 'removeCalcsInit',
    addFnName: 'addCalcsInit',
    modifyFnName: 'modifyCalcsInit',
    handleEventName: 'onCalcsInitialOChanged',
    ...commonFnName
  },
  [CalcMode.Final]: {
    readFnName: 'readCalcsFinalOptions',
    removeFnName: 'removeCalcsFinal',
    addFnName: 'addCalcsFinal',
    modifyFnName: 'modifyCalcsFinal',
    handleEventName: 'onCalcsFinalChanged',
    ...commonFnName
  },
  [CalcMode.Signal]: {
    readFnName: 'readCalcsSignalOptions',
    removeFnName: 'removeCalcsSignal',
    addFnName: 'addCalcsSignal',
    modifyFnName: 'modifyCalcsSignal',
    handleEventName: 'onCalcsSignalChanged',
    ...commonFnName
  }
}
export const calcContextKey: InjectionKey<calcContext> = Symbol('calcContextKey')
