<template>
  <MyDialog v-model="isActive" title="PLC Calculation Editor" width="700px" @ok="onSave">
    <div :class="e('body')">
      <div style="margin-bottom: 16px">
        <div>Signal: {{ signal }}</div>
        <div>Source: {{ source }}</div>
      </div>
      <div style="display: flex; align-items: center; margin-bottom: 16px">
        <span style="margin-right: 16px">Polynomial degree</span>
        <wui-select v-model="degree" style="width: 80px; margin-right: 32px">
          <wui-option v-for="n in 10" :key="n" :value="n" :label="n" />
        </wui-select>
        <span style="margin-right: 8px">X Range</span>
        <wui-input v-model="xMin" placeholder="Min" style="width: 120px; margin-right: 8px" />
        <wui-input v-model="xMax" placeholder="Max" style="width: 120px" />
      </div>
      <div :class="e('calc')">
        <template v-for="n in degree" :key="n">
          <wui-input v-model="coeffs[degree - n]" style="width: 100px" />
          <span v-if="degree - n > 0" style="margin: 0 4px"
            >X<sup>{{ degree - n }}</sup> ×</span
          >
        </template>
        <wui-input v-model="coeffs[degree]" style="width: 100px" />
      </div>
    </div>
  </MyDialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import MyDialog from '@/renderer/components/dialog/index.vue'
import $styles from '../index.module.scss'
import { useBem } from '@/renderer/hooks/bem'

const { e } = useBem('plc-signal-poly', $styles)

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  signal: { type: String, default: '' },
  source: { type: String, default: '' },
  degreeDefault: { type: Number, default: 10 }
})
const emit = defineEmits(['update:modelValue', 'save'])

const isActive = ref(props.modelValue)
watch(
  () => props.modelValue,
  v => (isActive.value = v)
)
watch(isActive, v => emit('update:modelValue', v))

const degree = ref(props.degreeDefault)
const xMin = ref('10000.00')
const xMax = ref('10000000')
const coeffs = reactive(Array.from({ length: 11 }, () => '1.00000'))

const onSave = () => {
  emit('save', {
    degree: degree.value,
    xMin: xMin.value,
    xMax: xMax.value,
    coeffs: [...coeffs]
  })
  isActive.value = false
}
</script>
