<template>
  <MyDialog v-model="isActive" title="PLC Channel Editor" width="800px" @ok="onSubmit">
    <div :class="styles.modelBox">
      <wui-table
        border
        :data="tableData"
        style="width: 100%"
        height="350px"
        :header-cell-style="{
          background: '#EAF1FD',
          color: '#90AFE4',
          fontSize: '18px',
          fontWeight: 'bold'
        }"
        @row-contextmenu="handleRowMenu"
      >
        <wui-table-column label="Type" min-width="180px" align="center">
          <template #default="{ row }">
            <span>
              {{ convertType(channelTypeOptions, row.type) }}
            </span>
          </template>
        </wui-table-column>

        <wui-table-column label="Address" min-width="120px" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.addr }}</span>
          </template>
        </wui-table-column>

        <template #empty>
          <TableTool.Empty />
        </template>
      </wui-table>
    </div>
  </MyDialog>

  <SignalEditor1 v-if="signalShow1" v-model:model-show="signalShow1" :params="signalParam" />
  <SignalEditor2 v-if="signalShow2" v-model:model-show="signalShow2" :params="signalParam" />
</template>

<script lang="ts" setup>
import { ref, inject } from 'vue'
import styles from '../index.module.scss'
import { useVModel } from '@vueuse/core'
import MyDialog from '@/renderer/components/dialog/index.vue'
import { convertType } from '@/renderer/utils/common'
import { useTableCommonMenu } from '@/renderer/hooks'
import SignalEditor1 from '../signalEditor1/index.vue'
import SignalEditor2 from '../signalEditor2/index.vue'
import TableTool from '@/renderer/components/TableTool'
import { PlcDevice, PlcSignals } from '@wuk/cfg'
import { SignalParam, PLCContext } from '../type'

const props = defineProps({
  currentPlcIndex: {
    type: Number,
    default: 0
  },
  modelShow: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelShow'])
const isActive = useVModel(props, 'modelShow', emit)

const plcContext = inject<PLCContext>('plcData')
const tableData = ref<PlcDevice[]>(plcContext?.plcList.value[props.currentPlcIndex].devices || [])

const channelTypeOptions = [
  {
    label: 'Discrete Output(%I)',
    value: 0
  },
  {
    label: 'Analog Input(%AI)',
    value: 1
  },
  {
    label: 'Discrete Output(%Q)',
    value: 2
  },
  {
    label: 'Analog Output(%AQ)',
    value: 3
  },
  {
    label: 'Register Input Ana(%R)',
    value: 4
  },
  {
    label: 'Register Output Ana(%R)',
    value: 5
  },
  {
    label: 'Discrete Input(%Q)',
    value: 6
  },
  {
    label: 'Register Output Disc(%R)',
    value: 7
  },
  {
    label: 'Register Input Disc(%R)',
    value: 8
  },
  {
    label: 'Digital 2 Word Input(%R)',
    value: 9
  }
]
const signalShow1 = ref(false)
const signalShow2 = ref(false)
const signalParam = ref<SignalParam>()

const { handleRowMenu, handleTableAreaCtxMenu } = useTableCommonMenu(
  tableData,
  async (key, ...args) => {
    const { row, rowIndex } = args[0] || {}
    switch (key) {
      case 'signalsKey':
        if (row) {
          openSignalEditor(row, rowIndex)
        }
        break
      default:
        break
    }
  },
  [1],
  [{ key: 'signalsKey', label: 'signals' }]
)

const openSignalEditor = (row: PlcDevice, index: number) => {
  signalParam.value = {
    plcDeviceIndex: index,
    currentPlcIndex: props.currentPlcIndex
  }
  row.type === 1 ? (signalShow2.value = true) : (signalShow1.value = true)
}

// 处理来自 SignalEditor 的数据更新
const handleSignalDataUpdate = async (updateData: any) => {
  const { deviceIndex, signalData, plcSetupIndex } = updateData

  // 更新本地设备数据
  if (tableData.value[deviceIndex]) {
    Object.assign(tableData.value[deviceIndex].signals, signalData)
  }

  // 直接调用父组件提供的修改方法
  if (plcContext?.modifyPLCSetup) {
    const modificationData = {
      devices: tableData.value
    }
    await plcContext.modifyPLCSetup(plcSetupIndex, modificationData)
  }
}

const onSubmit = async () => {
  isActive.value = false
}
</script>
