<template>
  <div :class="styles.box">
    <div :class="styles.box_table" class="cfg-setup_table">
      <wui-table border :data="plcList" height="100%" @row-contextmenu="handleRowMenu">
        <wui-table-column
          label="Device Name"
          min-width="120px"
          align="center"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span>{{ row.device_name }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Scan Rate" min-width="100px" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.scan_rate }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="PLC Type" min-width="100px" align="center" show-overflow-tooltip>
          <template #default>
            <span>MODBUS TCP</span>
          </template>
        </wui-table-column>
        <wui-table-column
          label="Engine Specific"
          min-width="100px"
          align="center"
          show-overflow-tooltip
        >
          <template #default>
            <span>{{ engineName }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Test Mode" min-width="180px" align="center">
          <template #default="{ row }">
            <wui-select
              v-if="row.flag"
              v-model="row.test_mode"
              placeholder="Select test mode"
              style="width: 100%"
            >
              <wui-option
                v-for="item in testModeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </wui-select>
            <span v-else>{{ convertType(testModeOptions, row.test_mode) }}</span>
          </template>
        </wui-table-column>
        <wui-table-column label="Op" fixed="right" width="100px" align="center">
          <template #default="{ row, $index }">
            <TableTool.Op :flag="row.flag" @op="handleOp($event, row, $index)">
              <template #pre-op="{ opStyle }">
                <wui-icon :style="opStyle" @click="openChannelEditor(row, $index)">
                  <Edit />
                </wui-icon>
              </template>
            </TableTool.Op>
          </template>
        </wui-table-column>
        <template #empty>
          <TableTool.Empty @custom-contextmenu="handleTableAreaCtxMenu" />
        </template>
      </wui-table>
    </div>
  </div>

  <ChannelEditor
    v-if="channelShow"
    v-model:model-show="channelShow"
    :current-plc-index="currentPlcIndex"
  />
</template>

<script lang="ts" setup>
import { onMounted, ref, provide } from 'vue'
import styles from './index.module.scss'
import ChannelEditor from './channelEditor/index.vue'
import { Edit, Plus } from '@element-plus/icons-vue'
import { PlcCfgOption, PlcDriverOptions } from '@wuk/cfg'
import { useBizEngine, useHandler, useTableCommonMenu, useBizMain } from '@/renderer/hooks'
import { BizEngine } from '@/renderer/logic'
import { WuiMessage } from '@wuk/wui'
import { convertType } from '@/renderer/utils/common'
import TableTool, { OpType, isAddOrInsertType, RowType } from '@/renderer/components/TableTool'
import type { PLCContext } from './type'

const channelShow = ref(false)
const currentPlcIndex = ref()
const mainPtr = useBizMain()
const engineName = mainPtr.value?.engine?.name || ''
const plcTypeOptions = [
  {
    label: 'MODBUS TCP',
    value: '1'
  }
]
const engineOptions = [
  {
    label: engineName,
    value: engineName
  }
]
const testModeOptions = [
  {
    label: 'No Device Hardware',
    value: 0
  },
  {
    label: 'Test With Device Hardware',
    value: 1
  }
]
interface newPLCSetupItem extends PlcCfgOption {
  meta?: PlcCfgOption
  flag: boolean
  row_type: RowType
}
const plcPtr = useBizEngine()
const plcList = ref<newPLCSetupItem[]>([])

const tipsMessage = () => {
  WuiMessage({
    message: 'success',
    type: 'success',
    offset: 80
  })
}

// 使用标准的表格菜单钩子
const { handleRowMenu, handleTableAreaCtxMenu } = useTableCommonMenu(
  plcList,
  async (key, ...args) => {
    const { row, rowIndex } = args[0]
    switch (key) {
      case 'modifyKey':
        handleOp('edit', row, rowIndex)
        break
      case 'channelsKey':
        openChannelEditor(row, rowIndex)
        break
      default:
        break
    }
  },
  [1],
  [
    { key: 'modifyKey', label: 'modify' },
    { key: 'channelsKey', label: 'channels' }
  ]
)

// 操作处理函数
const handleOp = async (op: OpType, row: newPLCSetupItem, index: number) => {
  switch (op) {
    case 'edit':
      if (row.flag) return
      row.meta = { ...row }
      row.flag = true
      break
    case 'select':
      await onConfirm(row, index)
      break
    case 'cancel':
      onCancel(row, index)
      break
  }
}

const onCancel = (item: newPLCSetupItem, index: number) => {
  const { row_type, meta = {} } = item
  Object.assign(item, meta)
  item.flag = false
  delete item.meta
}

const onConfirm = async (item: newPLCSetupItem, index: number) => {
  const { device_name, scan_rate } = item
  if (!device_name) {
    WuiMessage({
      message: 'Device name cannot be empty',
      type: 'warning',
      offset: 80
    })
    return
  }
  const data = { device_name, scan_rate }
  let editResult
  editResult = await plcPtr.value?.modifyPLCSetup(index, data)
  if (!editResult) return
  item.row_type = '*'
  item.flag = false
  tipsMessage()
}

// 提供给子组件的修改方法
const modifyPLCSetupData = async (plcSetupIndex: number, modifiedData: any) => {
  const editResult = await plcPtr.value?.modifyPLCSetup(plcSetupIndex, modifiedData)
  if (editResult) {
    // 重新获取数据以确保同步
    await getDataInfo()
    tipsMessage()
    return true
  }
  return false
}

// 使用 provide 提供数据和修改方法给子组件
const plcContext: PLCContext = {
  plcList,
  modifyPLCSetup: modifyPLCSetupData
}
provide('plcData', plcContext)

const openChannelEditor = (row: newPLCSetupItem, index: number) => {
  currentPlcIndex.value = index
  channelShow.value = true
}

const getDataInfo = async () => {
  const { plcs = [] } = (await plcPtr.value?.readPLCSetups()) || ({} as PlcDriverOptions)
  plcList.value = plcs.map(item => {
    const meta = item
    const flag = false
    const row_type: RowType = '*'
    return { ...item, meta, flag, row_type }
  })
}

useHandler(plcPtr, BizEngine.onPLCSetupOptionsChanged, getDataInfo)

onMounted(async () => {
  await getDataInfo()
})
</script>
