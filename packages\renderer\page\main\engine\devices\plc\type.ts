import { BaseTableRow } from '@/renderer/components/TableTool'
import { PlcSignal, PlcSignals, PlcCfgOption } from '@wuk/cfg'
import { Ref } from 'vue'

export interface SignalParam {
  currentPlcIndex: number
  plcDeviceIndex: number
}

export type PlcSignalTableRow = BaseTableRow<PlcSignal & { meta: PlcSignal }>

export interface PLCContext {
  plcList: Ref<PlcCfgOption[]>
  modifyPLCSetup: (plcSetupIndex: number, modifiedData: any) => Promise<boolean>
}
