import { TreeContent, TreeNode, TreeProps } from '../../TreeContent'
import { computed, defineComponent, onMounted, PropType, ref, provide, reactive, toRef } from 'vue'
import { SetupEquation, SetupGroup } from './index'
import { useBizEngine } from '@/renderer/hooks'
import { CfgFileType, uuid } from '@wuk/cfg'
import { CalcMode, CalcModeType, CalcTree, CurEditCalcInfo, calcContextKey } from './constants'
import { useBiz } from './hooks'
export default defineComponent({
  name: 'Calc',
  props: {
    calcMode: {
      type: String as PropType<CalcModeType>,
      readonly: true,
      required: true
    }
  },
  setup(props) {
    const bizCalcs = useBiz(props.calcMode)
    // const calcData = ref<CalsSignalItem[]>([])
    const treeData = ref<CalcTree[]>([])
    const treeContentRef = ref<InstanceType<typeof TreeContent>>()
    const enginePtr = useBizEngine()
    const defaultExpandedKeys = ref<number[]>([0])
    /**
     * @type {*}
     * @param {CalcsSignalType | CalcsExcuteType} calcId 一级节点id
     * @param {number} groupNodeIndex 二级节点index
     * */
    const curEditCalcInfo = ref<CurEditCalcInfo>({
      label: '',
      calcId: -1,
      index: 0,
      groupNodeIndex: -1,
      children: []
    })
    /**
     * @description 二级节点 label
     */
    const groupNodeLabel = ref('')
    const mode = ref<'setup-group' | 'setup-equation'>('setup-group')
    const handleNodeChange = (data: CalcTree, node: TreeNode) => {
      const { level, parent } = node
      if (level === 1) {
        mode.value = 'setup-group'
        curEditCalcInfo.value = {
          ...data,
          calcId: data.id,
          groupNodeIndex: -1
        }
      } else if (level === 2) {
        const { data: parentData } = parent
        mode.value = 'setup-equation'
        curEditCalcInfo.value = {
          ...(parentData as CalcTree),
          calcId: parentData.id,
          groupNodeIndex: data.index ?? -1
        }
        groupNodeLabel.value = data.label
      }
    }
    const isGroupMode = computed(() => mode.value === 'setup-group')
    /**
     * @description 获取计算信号
     */
    const getCalcsSignalOptions = async () => {
      if (props.calcMode === CalcMode.Signal) {
        treeData.value = [
          {
            label: 'Calibrate',
            id: CfgFileType.Calibrate,
            index: 0,
            children: []
          }
        ]
      } else {
        treeData.value = [
          {
            label: 'Common',
            id: CfgFileType.Common,
            index: 0,
            children: []
          },
          {
            label: 'Specific',
            id: CfgFileType.EngineSpecific,
            index: 1,
            children: []
          }
        ]
      }
      const groupList = (await bizCalcs.readCalcs?.()) || []
      console.log(groupList, '!!!!!!')
      groupList.forEach(item => {
        const idx = item.type === CfgFileType.EngineSpecific ? 1 : 0
        const treeItem = treeData.value[idx]
        const len = treeItem.children?.length || 0
        treeItem.children?.push({
          label: item.group_name,
          id: uuid(),
          index: len,
          originData: {
            ...item
          }
        })
      })
      const { id, ...args } = treeData.value[curEditCalcInfo.value.index ?? 0]
      curEditCalcInfo.value = {
        calcId: id,
        ...args
      }
    }
    /**
     * @description modify calc group
     */
    const changeTreeNode = async (id: number) => {
      treeContentRef.value?.treeRef?.setCurrentKey(id)
    }
    const handleAllowDrag: TreeProps['allowDrag'] = node => {
      const { level } = node
      if (level === 1) return false
      if (level === 2) return true
      return false
    }
    const handleAllowDrop: TreeProps['allowDrop'] = (_, dropNode, type) => {
      const { level } = dropNode
      if (level === 1) {
        if (type === 'inner') return true
        return false
      }
      if (level === 2) {
        if (type === 'inner') return false
        return true
      }
      return false
    }
    bizCalcs.bindHandler(getCalcsSignalOptions)
    onMounted(async () => {
      await getCalcsSignalOptions()
      treeContentRef.value?.treeRef?.setCurrentKey(0)
    })
    provide(
      calcContextKey,
      reactive({
        bizCalcs,
        calcMode: toRef(props.calcMode),
        curEditCalcInfo,
        changeTreeNode
      })
    )
    return () => {
      return (
        <>
          <TreeContent
            ref={treeContentRef}
            onTree-node-change={handleNodeChange}
            treeData={treeData.value}
            treeAreaMenu={[]}
            draggable={true}
            showRightMenu={false}
            rightContentPadding={0}
            allowDrag={handleAllowDrag}
            allowDrop={handleAllowDrop}
            v-model:default-expanded-keys={defaultExpandedKeys.value}>
            {{
              header: () =>
                isGroupMode.value ? (
                  <SetupGroup.Header />
                ) : (
                  <SetupEquation.Header label={groupNodeLabel.value} />
                ),
              default: () => (isGroupMode.value ? <SetupGroup.Table /> : <SetupEquation.Table />)
            }}
          </TreeContent>
        </>
      )
    }
  }
})
