import { computed } from 'vue'
import { useBizEngine, useBizMain, useHandler } from '@/renderer/hooks'
import { CalcMode, calcModefnMap, CalcModeType } from '../constants'
import { BizEngine, BizMain } from '@/renderer/logic'

/**
 * @description 决定接口调用的模块 main | engine
 */
export const useBiz = (mode: CalcModeType) => {
  const bizEngine = useBizEngine()
  // const bizMain = useBizMain()
  // todo:::
  const biz = computed(() => (mode === CalcMode.Sys_common ? bizEngine.value : bizEngine.value))
  const fnPtr = computed(() => calcModefnMap[mode])
  const bindHandler = (handler: (...args: any[]) => void) => {
    const BizClass = mode === CalcMode.Sys_common ? BizEngine : BizEngine
    useHandler(biz, BizClass[fnPtr.value.handleEventName], handler)
  }
  return {
    readCalcs: biz.value?.[fnPtr.value.readFnName].bind(biz.value), // group 读取计算
    modifyCalcs: biz.value?.[fnPtr.value.modifyFnName].bind(biz.value), // group 修改计算
    removeCalcs: biz.value?.[fnPtr.value.removeFnName].bind(biz.value), // group 删除计算
    addCalcs: biz.value?.[fnPtr.value.addFnName].bind(biz.value), // group 新增计算
    loadCalc: biz.value?.[fnPtr.value.loadCalcName].bind(biz.value), // group item加载计算
    modifyCalc: biz.value?.[fnPtr.value.modifyCalcName].bind(biz.value), // group item修改计算
    saveCalc: biz.value?.[fnPtr.value.saveCalcName].bind(biz.value), // group item保存计算
    bindHandler
  }
}
